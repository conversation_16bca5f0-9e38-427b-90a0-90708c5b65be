const { COLLECTIONS } = require("../init");
const { getLeadById } = require("../post");
const { sendEmail } = require("../resend");
const { replaceShortCodes } = require("../shortcodes");

function prepareEmailToSaveinRedis(email) {
  const { contacts } = email;
  if (!contacts || !contacts.length) {
    return;
  }
  for (const contactId of contacts) {
    const emailPrepared = prepareEmailForContact(contactId, email);
    sendEmail(emailPrepared, (result) => {
      console.log("EMAILCRON > Result", result);
    });
  }
}

async function prepareEmailForContact(contactId, email) {
  const lead = await getLeadById(contactId);

  if (!lead) {
    console.error(
      `EMAILCRON > PREPARE EMAIL > Lead not found for contact ID: ${contactId}`
    );
    return null;
  }

  // Substituir shortcodes na mensagem
  const messageContent = await replaceShortCodes(
    email.body,
    [],
    COLLECTIONS.LEADS_COLLECTION_NAME,
    [lead]
  );

  console.log("EMAILCRON > PREPARED EMAIL > CONTENT", messageContent);

  const emailPrepared = {
    ...email,
    body: messageContent,
    leadId: contactId,
    leadData: {
      name: lead.displayName || lead.name || "",
      email: lead.email || "",
      phone: lead.mobile || "",
    },
    to: lead.email,
  };
  console.log("EMAILCRON > PREPARED EMAIL", emailPrepared);
  return emailPrepared;
}

module.exports = {
  prepareEmailToSaveinRedis,
};
