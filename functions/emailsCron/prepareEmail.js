const { COLLECTIONS, FirestoreRef } = require("../init");
const { email } = require("../models/leadWebhook");
const { getLeadById } = require("../post");
const { sendEmail } = require("../resend");
const { replaceShortCodes } = require("../shortcodes");
const { saveMessageInRedis } = require("../shotxOrganize");
const { saveMessage, saveScheduledMessage } = require("../utils/redisClient");

async function prepareEmailToSaveinRedis(email) {
  const { contacts } = email;
  console.log("EMAILCRON > PREPARE EMAIL > EMAIL", email);
  if ((!contacts || !contacts.length) && !email.segmentations) {
    console.log("EMAILCRON > PREPARE EMAIL > NO CONTACTS");
    return;
  }
  let index = 1;
  for (const contactId of contacts) {
    const emailPrepared = await prepareEmailForContact(
      contactId,
      email,
      contacts.length,
      index
    );
    if (emailPrepared) {
      console.log("EMAILCRON > PREPARED EMAIL > EMAILPREPARED", emailPrepared);
      console.log("EMAILCRON > PREPARED EMAIL > EMAILPREPARED > Index", index);
      saveEmailInRedis(emailPrepared);
    }
    index++;
  }
  FirestoreRef.collection(COLLECTIONS.MAIL_COLLECTION_NAME)
    .where("ID", "==", email.ID)
    .doc(email.ID)
    .update({
      prepared: true,
    });
}

async function prepareEmailForContact(contactId, email, qtdAllLeads, index) {
  const lead = await getLeadById(contactId);

  if (!lead) {
    console.error(
      `EMAILCRON > PREPARE EMAIL > Lead not found for contact ID: ${contactId}`
    );
    return null;
  }
  // Substituir shortcodes na mensagem
  const messageContent = await replaceShortCodes(
    email.html,
    [],
    COLLECTIONS.LEADS_COLLECTION_NAME,
    [lead]
  );

  console.log("EMAILCRON > PREPARED EMAIL > CONTENT", messageContent);

  if (lead.email) {
    const emailPrepared = {
      ...email,
      html: messageContent.content,
      leadId: contactId,
      leadData: {
        name: lead.displayName || lead.name || "",
        email: lead.email || "",
        phone: lead.mobile || "",
      },
      to: lead.email,
      qtdLeads: qtdAllLeads,
      index: index,
    };
    console.log("EMAILCRON > PREPARED EMAIL", emailPrepared);
    return emailPrepared;
  }
  console.log("EMAILCRON > PREPARED EMAIL > NO EMAIL ON LEAD");
  return null;
}

async function saveEmailInRedis(emailPrepared) {
  const emailIdToSaveInRedis = `email:${emailPrepared.ID}_${emailPrepared.leadId}_${Date.now()}`;
  const scheduledDate = new Date(emailPrepared.scheduled_date).getTime();
  const emailList = "email:scheduled_emails";
  const emailWitheMetadata = {
    ...emailPrepared,
    id: emailIdToSaveInRedis,
    _scheduled_timestamp: scheduledDate,
    _scheduled_iso: emailPrepared.scheduled_date,
    _created_at: new Date().toISOString(),
  };
  const emailSaved = await saveScheduledMessage(
    emailList,
    scheduledDate,
    emailIdToSaveInRedis,
    emailWitheMetadata
  );
  console.log("EMAILCRON > SAVE EMAIL IN REDIS > EMAILSAVED", emailSaved);
}

module.exports = {
  prepareEmailToSaveinRedis,
};
