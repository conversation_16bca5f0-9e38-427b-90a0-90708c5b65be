const {
  getScheduledMessages,
  removeMessage,
  removeAllMessages,
} = require("../utils/redisClient");
const { FirestoreRef, CONSTANTS } = require("../init");
const { momentNow } = require("../helpers");
const moment = require("moment");
const axios = require("axios");
const dotenv = require("dotenv");
const { sendEmail } = require("../resend");

// Carregar variáveis de ambiente do arquivo .env
dotenv.config();

/**
 * Processa mensagens agendadas do Redis e envia para o chat-api
 * @param {Object} options - Opções de processamento
 * @param {number} options.batchSize - Número máximo de mensagens a processar por vez (default: 50)
 * @param {boolean} options.dryRun - Se true, não envia as mensagens, apenas simula (default: true)
 * @param {boolean} options.simulateOnly - Se true, apenas simula o envio com logs detalhados (default: true)
 * @returns {Promise<Array>} Lista de mensagens processadas
 */
const sendEmails = async (options = {}) => {
  try {
    // Processar as mensagens agendadas
    const processedMessages = await processScheduledEmails(options);

    console.log(
      `EMAILCRON > SHOTX SEND MESSAGES > Processadas ${processedMessages.length} mensagens`
    );

    return processedMessages;
  } catch (error) {
    console.error(
      "EMAILCRON > SHOTX SEND MESSAGES > Erro no processamento:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};
const processScheduledEmails = async (options = {}) => {
  // Definir opções padrão - simulação ativada por padrão
  const {
    batchSize = 50,
    dryRun = false, // Alterado para true por padrão
    simulateOnly = false, // Nova opção para apenas simular o envio
  } = options;

  console.log(
    `EMAILCRON > PROCESS MESSAGES > START > Modo: ${simulateOnly ? "SIMULAÇÃO" : "PRODUÇÃO"}, DryRun: ${dryRun ? "SIM" : "NÃO"}`
  );

  try {
    // Chave da lista ordenada de mensagens agendadas
    const scheduledListKey = "email:scheduled_emails";

    let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
    let momentNowInstanceTimestamp = new Date(momentNowISO).getTime();
    // removeAllMessages(scheduledListKey);
    //Obtem as mensagens agendadas no Redis
    const messages = await getScheduledMessages(
      scheduledListKey,
      momentNowInstanceTimestamp,
      "PROCESS",
      {
        limit: batchSize,
        remove: false,
      }
    );

    if (messages.length === 0) {
      console.log("EMAILCRON > PROCESS MESSAGES > NO MESSAGES");
      return [];
    }

    const totalMessages = messages.length;
    console.log(
      `EMAILCRON > PROCESS MESSAGES > TOTAL MESSAGES ${totalMessages} TO PROCESS`
    );

    let messageIndex = 0;
    // Processar mensagens em paralelo para melhor performance
    let messagesProcesseds = [];
    const messagesProcess = messages.map(async (message) => {
      let results = {};
      try {
        const messageId =
          message.id ||
          `msg_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

        if (dryRun) {
          console.log(
            `EMAILCRON > PROCESS MESSAGES > [DRY RUN] Would send message ${messageId}`
          );

          //   // Atualizar status no Firestore
          //   await FirestoreRef.collection("shotx_messages_sent")
          //     .doc(messageId)
          //     .update({
          //       status: "simulated",
          //       _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
          //     });

          // Usar a redis_key da mensagem para remoção correta
          if (message.redis_key) {
            await removeMessage(message.redis_key, "shotx:scheduled_messages");
          }
          return { ...message, id: messageId, status: "simulated" };
        }
        console.log(`EMAILCRON > PROCESS MESSAGES > MESSAGE`, message);

        messagesProcesseds.push(message);

        messageIndex += 1;

        if (messageIndex === totalMessages) {
          console.log(
            `EMAILCRON > PROCESS MESSAGES > IF COMPARE MATCHED - SENDING MESSAGES`
          );
          //TODO send email aqui
          console.log("EMAILCRON > MESSAGE", message);
          // const results = await sendEmail(message, (result) => {
          //   console.log("EMAILCRON > Result", result);
          // });
          if (results && !results.hasError) {
            console.log(`EMAILCRON > PROCESS MESSAGES > ENVIO BEM-SUCEDIDO`);
          } else {
            console.warn(
              `EMAILCRON > PROCESS MESSAGES > FALHA NO ENVIO - MENSAGENS MANTIDAS NO REDIS`
            );
          }
        }
        return results;
      } catch (error) {
        console.error(
          `EMAILCRON > ERROR TO SEND MESSAGES > ERROR:`,
          error.message
        );
        return null;
      }
    });

    return messagesProcess;
  } catch (error) {
    console.error(
      "EMAILCRON > PROCESS MESSAGES > Fatal error during processing:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Limpa todas as mensagens agendadas do Redis
 * @returns {Promise<number>} - Número de mensagens removidas
 */
const clearAllMessages = async () => {
  try {
    console.log(
      "EMAILCRON > CLEAR ALL MESSAGES > Iniciando limpeza de mensagens"
    );

    // Chave da lista ordenada de mensagens agendadas
    const scheduledListKey = "email:scheduled_emails";

    // Remover todas as mensagens
    const removedCount = await removeAllMessages(scheduledListKey);

    console.log(
      `EMAILCRON > CLEAR ALL MESSAGES > Removidas ${removedCount} mensagens`
    );

    return removedCount;
  } catch (error) {
    console.error("EMAILCRON > CLEAR ALL MESSAGES > ERROR:", error);
    return 0;
  }
};

/**
 * Remove uma mensagem específica pelo ID
 * @param {string} messageId - ID da mensagem a ser removida
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const deleteMessage = async (messageId) => {
  try {
    if (!messageId) {
      console.error(
        "EMAILCRON > DELETE MESSAGE > ID da mensagem é obrigatório"
      );
      return false;
    }

    // Formatar a chave da mensagem
    const messageKey = messageId.startsWith("shotx:")
      ? messageId
      : `shotx:message:${messageId}`;

    // Chave da lista ordenada de mensagens agendadas
    const scheduledListKey = "shotx:scheduled_messages";

    // Remover a mensagem
    const success = await removeMessage(messageKey, scheduledListKey);

    if (success) {
      console.log(
        `EMAILCRON > DELETE MESSAGE > Mensagem ${messageId} removida com sucesso`
      );
    } else {
      console.error(
        `EMAILCRON > DELETE MESSAGE > Falha ao remover mensagem ${messageId}`
      );
    }

    return success;
  } catch (error) {
    console.error(`EMAILCRON > DELETE MESSAGE > ERROR:`, error);
    return false;
  }
};

module.exports = {
  sendEmails,
};
